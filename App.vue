<script>
	/**
	 * vuex管理登录状态，具体可以参考官方登录模板示例
	 */
	import config from "@/config/config";
	import {
		getClipboardData
	} from "@/js_sdk/h5-copy/h5-copy.js";
	import APPUpdate from "@/plugins/APPUpdate";
	import storage from "@/utils/storage";
	import {
		mapMutations
	} from "vuex";
	import { reportJPushRegId } from "@/api/common.js";
	// #ifdef APP-PLUS
	var jpushModule = uni.requireNativePlugin("JG-JPush");
	// #endif
	/**
	 * 路由监听并删除路由
	 * https://developers.weixin.qq.com/miniprogram/dev/api/route/wx.navigateTo.html
	 * */
	// #ifdef MP-WEIXIN
	wx.onAppRoute((res) => {});
	// #endif

	export default {
		data() {
			return {
				config,
				notificationPermissionChecked: false, // 是否已检测过权限
				notificationPermissionGranted: false, // 权限是否已授予
			};
		},

		/**
		 * 监听返回
		 */
		onBackPress(e) {
			console.log("onBackPress-APP", e);
			if (e.from == "backbutton") {
				let routes = getCurrentPages();
				let curRoute = routes[routes.length - 1].options;
				routes.forEach((item) => {
					if (
						item.route == "pages/tabbar/cart/cartList" ||
						item.route.indexOf("pages/product/goods") != -1
					) {
						uni.redirectTo({
							url: item.route,
						});
					}
				});

				if (curRoute.addId) {
					uni.reLaunch({
						url: "/pages/tabbar/cart/cartList",
					});
				} else {
					uni.navigateBack();
				}
				return true; //阻止默认返回行为
			}
		},
		methods: {
			...mapMutations(["login", "setShowEnjoyCard"]),
			
			/**
			 * 上报极光推送注册ID
			 * @param {string} regId 注册ID
			 * @param {string} clientType 终端类型
			 */
			
			reportJPushRegId(regId) {
				 // 根据平台设置客户端类型
				const platform = uni.getSystemInfoSync().platform;
				let paymentType;
				if (platform === "android") {
					paymentType = "ANDROID";
				} else if (platform === "ios") {
					paymentType = "IOS";
				} else {
					paymentType = "APP";
				}
				if (regId) {
					reportJPushRegId(regId,paymentType).then(res => {
						console.log('极光推送注册ID上报成功:', res);
					}).catch(err => {
						console.error('极光推送注册ID上报失败:', err);
					});
				}
			},

			/**
			 * 微信小程序版本提交更新版本 解决缓存问题
			 */
			applyUpdateWeChat() {
				const updateManager = uni.getUpdateManager();

				updateManager.onCheckForUpdate(function(res) {
					// 请求完新版本信息的回调
				});

				updateManager.onUpdateReady(function(res) {
					uni.showModal({
						title: "更新提示",
						content: "发现新版本，是否重启应用？",
						success(res) {
							if (res.confirm) {
								// 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
								updateManager.applyUpdate();
							}
						},
					});
				});
				updateManager.onUpdateFailed(function(res) {
					// 新的版本下载失败
				});
			},

			//  TODO 开屏广告 后续优化添加
			launch() {
				// 检查版本更新
				APPUpdate();
			},

			/**
			 * 获取粘贴板数据
			 */
			async getClipboard() {
				let res = await getClipboardData();

				/**
				 * 解析粘贴板数据
				 */

				if (
					res.indexOf(config.shareLink) != -1 &&
					res != this.$store.state.shareLink
				) {
					this.$store.state.shareLink = res;
					uni.showModal({
						title: "提示",
						content: "检测到一个分享链接是否跳转？",
						confirmText: "跳转",
						success: function(callback) {
							if (callback.confirm) {
								const path = res.split(config.shareLink)[1];
								if (path.indexOf("tabbar") != -1) {
									uni.switchTab({
										url: path,
									});
								} else {
									uni.navigateTo({
										url: path,
									});
								}
							}
						},
					});
				}
			},

			/**
			 * h5中打开app获取跳转app的链接并跳转
			 */
			checkArguments() {
				// #ifdef APP-PLUS
				setTimeout(() => {
					const args = plus.runtime.arguments;
					if (args) {
						const argsStr = decodeURIComponent(args);
						const path = argsStr.split("//")[1];
						if (path.indexOf("tabbar") != -1) {
							uni.switchTab({
								url: `/${path}`,
							});
						} else {
							uni.navigateTo({
								url: `/${path}`,
							});
						}
					}
				});
				// #endif
			},
			/**
			 * 手动刷新权限状态（用户主动触发）
			 */
			refreshPermissionStatus() {
				this.notificationPermissionChecked = false;
				this.notificationPermissionGranted = false;
				// 清除用户拒绝状态，允许重新检测权限
				uni.removeStorageSync('userDeclinedNotificationPermission');
				this.checkNotificationPermission();
			},

			/**
			 * 极光推送权限检测（智能检测，避免重复提醒）
			 */
			async checkNotificationPermission() {
				// 如果已经检测过且权限已授予，直接返回
				if (this.notificationPermissionChecked && this.notificationPermissionGranted) {
					return;
				}
				
				// 如果用户之前选择过"稍后再说"，则永远不再提醒（除非重新安装）
				const userDeclined = uni.getStorageSync('userDeclinedNotificationPermission');
				if (userDeclined) {
					console.log('用户之前选择过"稍后再说"，不再提醒权限');
					this.notificationPermissionChecked = true;
					this.notificationPermissionGranted = false;
					return;
				}

				try {
					if (uni.getSystemInfoSync().platform == "ios") {
						// iOS权限检测
						jpushModule.requestNotificationAuthorization((result) => {
							let status = result.status;
							this.notificationPermissionChecked = true;
							
							if (status >= 2) { // 2:允许
								this.notificationPermissionGranted = true;
								console.log('iOS通知权限已开启');
							} else {
								this.notificationPermissionGranted = false;
								// 显示权限引导
								this.showPermissionGuide();
							}
						});
					} else {
						// Android权限检测
						jpushModule.isNotificationEnabled((result) => {
							this.notificationPermissionChecked = true;
							
							if (result.code !== 0) { // 非0表示已开启权限
								this.notificationPermissionGranted = true;
								console.log('Android通知权限已开启');
							} else {
								this.notificationPermissionGranted = false;
								// 显示权限引导
								this.showPermissionGuide();
							}
						});
					}
				} catch (error) {
					console.error('检测通知权限失败:', error);
					this.notificationPermissionChecked = true;
				}
			},

			/**
			 * 显示权限引导（优化后的引导逻辑）
			 */
			showPermissionGuide() {
				const platform = uni.getSystemInfoSync().platform;
				
				if (platform == "ios") {
					// iOS引导到系统设置
					uni.showModal({
						title: '开启通知权限',
						content: '为了及时接收重要消息，建议您开启通知权限',
						showCancel: true,
						cancelText: '稍后再说',
						confirmText: '去设置',
						success: (res) => {
							if (res.confirm) {
								this.openIOSSettings();
							} else {
								// 用户点击"稍后再说"，记录用户拒绝状态，永远不再提醒
								uni.setStorageSync('userDeclinedNotificationPermission', true);
								console.log('用户选择"稍后再说"，记录拒绝状态');
							}
						}
					});
				} else {
					// Android引导到通知设置
					uni.showModal({
						title: '开启通知权限',
						content: '为了及时接收重要消息，建议您开启通知权限',
						showCancel: true,
						cancelText: '稍后再说',
						confirmText: '去设置',
						success: (res) => {
							if (res.confirm) {
								this.openAndroidSettings();
							} else {
								// 用户点击"稍后再说"，记录用户拒绝状态，永远不再提醒
								uni.setStorageSync('userDeclinedNotificationPermission', true);
								console.log('用户选择"稍后再说"，记录拒绝状态');
							}
						}
					});
				}
			},

			/**
			 * 打开iOS系统设置
			 */
			openIOSSettings() {
				try {
					var app = plus.ios.invoke('UIApplication', 'sharedApplication');
					var setting = plus.ios.invoke('NSURL', 'URLWithString:', 'app-settings:');
					plus.ios.invoke(app, 'openURL:', setting);
					plus.ios.deleteObject(setting);
					plus.ios.deleteObject(app);
				} catch (error) {
					console.error('打开iOS设置失败:', error);
					// 降级处理：提示用户手动设置
					uni.showToast({
						title: '请手动前往设置-通知-应用名称开启通知',
						icon: 'none',
						duration: 3000
					});
				}
			},

			/**
			 * 打开Android通知设置
			 */
			openAndroidSettings() {
				try {
					var main = plus.android.runtimeMainActivity();
					var pkName = main.getPackageName();
					var uid = main.getApplicationInfo().plusGetAttribute("uid");
					
					var Intent = plus.android.importClass('android.content.Intent');
					var Build = plus.android.importClass("android.os.Build");
					var intent;
					
					// Android 8.0及以上版本
					if (Build.VERSION.SDK_INT >= 26) {
						intent = new Intent('android.settings.APP_NOTIFICATION_SETTINGS');
						intent.putExtra('android.provider.extra.APP_PACKAGE', pkName);
					} else if (Build.VERSION.SDK_INT >= 21) { // Android 5.0-7.0
						intent = new Intent('android.settings.APP_NOTIFICATION_SETTINGS');
						intent.putExtra("app_package", pkName);
						intent.putExtra("app_uid", uid);
					} else { // Android 5.0以下
						var Settings = plus.android.importClass('android.provider.Settings');
						var Uri = plus.android.importClass('android.net.Uri');
						intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
						var uri = Uri.fromParts("package", pkName, null);
						intent.setData(uri);
					}
					
					main.startActivity(intent);
				} catch (error) {
					console.error('打开Android设置失败:', error);
					// 降级处理：提示用户手动设置
					uni.showToast({
						title: '请手动前往设置-应用-应用名称-通知开启通知',
						icon: 'none',
						duration: 3000
					});
				}
			},
		},
		onLaunch: function(val) {
			if (val.query.inviter) {
				storage.setInviter(val.query.inviter);
			}

			// #ifdef APP-PLUS
			// 重点是以下： 一定要监听后台恢复 ！一定要
			plus.globalEvent.addEventListener("newintent", (e) => {
				this.checkArguments(); // 检测启动参数
			});

			// 启动时检查版本更新
			this.launch();
			// #endif

			// #ifdef MP-WEIXIN
			this.applyUpdateWeChat();
			// #endif

			// #ifdef APP-PLUS

			
			jpushModule.setLoggerEnable(true);
			// 初始化函数
			jpushModule.initJPushService();
			jpushModule.addConnectEventListener(result => {
				let connectEnable = result.connectEnable
				console.log("jpush连接", connectEnable)
			})
			jpushModule.getRegistrationID(result => {
				console.log("注册ID.....", result)
				this.registerID = result.registerID
				
				// 上报极光推送注册ID
				this.reportJPushRegId(result.registerID);
				
				// uni.showToast({
				// 	title: result.registerID,
				// 	icon: "success",
				// })
			})
			jpushModule.isPushStopped(result => {
				let code = result.code
				console.log('连接状态回调', result)
			});
			// 设置别名
			jpushModule.setAlias({
				'alias': 'coder',
				'sequence': 1
			})
			jpushModule.addNotificationListener(result => {
				console.log('result--', result);
				let notificationEventType = result.notificationEventType
				let messageID = result.messageID
				let title = result.title
				let content = result.content
				let extras = result.extras
				console.log('通知事件回调', result)
				// 推送一个本地通知
				jpushModule.addLocalNotification({
					messageID,
					title,
					content,
					extras
				})
				if (notificationEventType === 'notificationOpened') {
					console.log('点击跳转--');
					uni.navigateTo({
						url: `/pages/index/test/test?id=${extras.testId}`
					})
				}
			})
			// 智能检测通知权限，避免重复提醒
			this.checkNotificationPermission();
			//#endif
		},

		onShow() {
			// #ifndef H5
			if (this.config.enableGetClipboard) {
				this.getClipboard();
			}
			// #endif

			
			// #ifdef APP-PLUS

			jpushModule.addNotificationListener(result => {
				let extras = result.extras
				console.log('onshow通知事件回调', result)
				if (result.notificationEventType === 'notificationOpened') {
					console.log('点击跳转--',extras);
					uni.switchTab({
						url: `/pages/tabbar/user/my?id=${extras.extras}`
					})
				}
			})
			// #endif

		},
	};
</script>

<style lang="scss">
	@import "uview-ui/index.scss";

	// -------适配底部安全区  苹果x系列刘海屏

	// #ifdef MP-WEIXIN
	.mp-iphonex-bottom {
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		box-sizing: content-box;
		height: auto !important;
		padding-top: 10rpx;
	}

	// #endif

	body {
		background-color: $bg-color;
	}

	/************************ */
	.w200 {
		width: 200rpx !important;
	}

	.flex1 {
		flex: 1; //必须父级设置flex
	}

	/* 通用解决方案（全平台） */
	.uni-tabbar-border {
		background-color: transparent !important;
		height: 0 !important;
		border: none !important;
	}

	/* 小程序平台强化处理 */
	/* #ifdef MP-WEIXIN */
	uni-tabbar .uni-tabbar__border {
		display: none !important;
	}

	/* #endif */

	/* 专门处理 APP 平台 */
	/* #ifdef APP-PLUS */
	/* 移除 iOS 阴影线 */
	.uni-tabbar.uni-tabbar--topselected {
		border-top: none !important;
	}

	/* 移除 Android 边框线 */
	.uni-tabbar {
		border-top-color: transparent !important;
		border-top-width: 0px !important;
		box-shadow: none !important;
	}

	/* 处理 iOS 15+ 新样式 */
	.uni-tabbar__content {
		background-image: none !important;
	}

	/* #endif */
</style>